{"name": "tripxplo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "serve": "next start"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/react-fontawesome": "^0.2.0", "@hookform/resolvers": "^3.9.0", "@next/env": "^14.2.9", "@next/third-parties": "^14.2.5", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.2.8", "@reduxjs/toolkit": "^2.2.1", "axios": "^1.7.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "embla-carousel-autoplay": "^8.0.0", "embla-carousel-react": "^8.0.0", "firebase": "^10.12.4", "history": "^5.3.0", "intro.js": "^7.2.0", "lucide-react": "^0.336.0", "next": "14.1.0", "next-auth": "^4.24.7", "react": "^18.2.0", "react-day-picker": "^8.10.0", "react-dom": "^18.2.0", "react-grid-gallery": "^1.0.1", "react-hook-form": "^7.52.1", "react-hot-toast": "^2.4.1", "react-icons": "^5.0.1", "react-otp-input": "^3.1.1", "react-query": "^3.39.3", "react-redux": "^9.1.0", "react-responsive": "^10.0.1", "react-slideshow-image": "^4.3.0", "react-toastify": "^10.0.5", "react-vertical-timeline-component": "^3.6.0", "reactjs-popup": "^2.0.6", "redux-persist": "^6.0.0", "swiper": "^11.1.4", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.1", "yup": "^1.4.0", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-vertical-timeline-component": "^3.3.6", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}