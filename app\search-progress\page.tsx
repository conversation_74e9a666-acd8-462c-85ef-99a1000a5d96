"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Search, MapPin, Calendar as CalendarIcon, Users, Briefcase, ArrowLeft, ArrowRight, CheckCircle2, Sparkles, XCircle, Users2, UsersRoundIcon, Star } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';
import { useDispatch } from 'react-redux';
import { changeDate, changeDestination, changeDestinationId } from '@/app/store/features/searchPackageSlice';
import { selectAdultsChild } from '@/app/store/features/roomCapacitySlice';
import { selectTheme, selectThemeId } from '@/app/store/features/selectThemeSlice';
import { useQuery } from 'react-query';
import { getInterest } from '@/app/actions/get-interest';
import { NEXT_PUBLIC_IMAGE_URL } from '@/app/utils/constants/apiUrls';
import { useMediaQuery } from 'react-responsive';

// Define types for booking data
interface BookingData {
    destination: string;
    destinationId: string;
    date: Date | undefined;
    rooms: number;
    adults: number;
    children: number;
    theme: string;
    themeId: string;
    departureCity?: string;
    tripType?: string;
    duration?: string;
}

// Define Interest interface
interface Interest {
    image: string;
    interestId: string;
    interestName: string;
    isFirst: boolean;
    sort: number;
    _id: string;
}

// Booking Progress Component with dynamic labels and navigation - Enhanced with modern UI improvements
const BookingProgress = ({ currentStep, bookingData, onNavigate }: { currentStep: number; bookingData: BookingData; onNavigate?: (step: number) => void }) => {
  const steps = [
    { label: "Destination", icon: MapPin },
    { label: "Date", icon: CalendarIcon },
    { label: "Rooms & Travelers", icon: Users },
  ];
  const isMobile = useMediaQuery({ query: '(max-width: 768px)' });

  return (
    <div className="bg-white/80 backdrop-blur-sm p-2 md:p-3 rounded-full shadow-md border border-gray-200/70 w-full md:w-auto">
      <div className="flex items-center justify-between md:justify-center space-x-1 md:space-x-3 text-xs md:text-base w-full">
        {steps.map((step, index) => (
          <React.Fragment key={index}>
            <button
              type="button"
              onClick={() => onNavigate && index <= currentStep && onNavigate(index)}
              disabled={index > currentStep}
              className={cn(
                "flex items-center font-semibold transition-colors duration-300 focus:outline-none text-center",
                currentStep === index ? "text-gray-900 font-bold" : "text-gray-400",
                index < currentStep ? "text-[#23cd92] cursor-pointer" : "cursor-default"
              )}
            >
              <step.icon className="hidden md:inline-block w-4 h-4 md:mr-2 shrink-0" />
              <span className="sm:ml-2">
                {index === 0 && (bookingData.destination ? bookingData.destination : "Destination")}
                {index === 1 && (bookingData.date ? (isMobile ? format(bookingData.date, "do MMM") : format(bookingData.date, "PPP")) : "Date")}
                {index === 2 && (isMobile ? "Travelers" : "Rooms & Travelers")}
              </span>
            </button>
            {index < steps.length - 1 && (
              <div className="h-4 w-px bg-gray-300 mx-1 md:mx-2"></div>
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

// Page Layout Template (with bottom-right CTA and dynamic progress)
const PageLayout = ({ children, currentStep, title, leftButton, bottomRightButton, backgroundImage, bookingData, onNavigate }: { children: React.ReactNode; currentStep: number; title: string; leftButton?: React.ReactNode; bottomRightButton?: React.ReactNode; backgroundImage?: string; bookingData: BookingData; onNavigate?: (step: number) => void }) => (
  <div
    className="min-h-screen bg-cover bg-center"
    style={{
      backgroundImage: backgroundImage ? `url(${backgroundImage})` : 'none',
    }}
  >
    <div className="min-h-screen bg-white bg-opacity-95 relative">
      <main className="container mx-auto px-4 py-8 md:py-12">
        {/* Progress Indicator and Navigation */}
        <div className="mb-8 md:mb-12 w-full">
          <div className="flex items-center justify-between gap-4">
            <div className="flex-shrink-0">{leftButton}</div>
            <div className="flex-1 max-w-3xl mx-4">
              <BookingProgress currentStep={currentStep} bookingData={bookingData} onNavigate={onNavigate} />
            </div>
            <div className="flex-shrink-0 w-[120px] hidden md:block" />
          </div>
        </div>

        {/* Page Header */}
        <div className="text-center mb-4 md:mb-16">
          <h1 className="text-lg lg:text-4xl sm:text-xl font-bold bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 bg-clip-text text-transparent tracking-tight mb-3 md:mb-4 flex items-center justify-center gap-2">
            {currentStep === 0 && <MapPin className="w-5 h-5 md:w-7 md:h-7 text-gray-800" />}
            {currentStep === 1 && <CalendarIcon className="w-5 h-5 md:w-7 md:h-7 text-gray-800" />}
            {currentStep === 2 && <Users className="w-5 h-5 md:w-7 md:h-7 text-gray-800" />}
            <span>{title}</span>
          </h1>
        </div>

        {/* Page Content */}
        {children}

        {/* Bottom Right CTA */}
      {bottomRightButton && (
        <div className="fixed bottom-4 left-4 right-4 md:bottom-24 md:right-40 md:left-auto md:transform-none z-50 hidden md:block">
          {bottomRightButton}
        </div>
      )}
      </main>
    </div>
  </div>
);

// Define Destination interface
interface Destination {
  destinationId: string;
  destinationName: string;
  image?: string;
  popular?: boolean;
  imageError?: boolean;
}

// 1. Destination Selection
const DestinationSelection = ({ onContinue, bookingData, onNavigate, setBookingData }: { onContinue: (destination: string, destinationId: string) => void; bookingData: BookingData; onNavigate: (step: number) => void; setBookingData: React.Dispatch<React.SetStateAction<BookingData>> }) => {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [destinationSearch, setDestinationSearch] = useState('');
  const [showDestinationPicker, setShowDestinationPicker] = useState(false);
  const [selectedDestination, setSelectedDestination] = useState<string | null>(bookingData.destination);
  const [selectedDestinationId, setSelectedDestinationId] = useState<string | null>(bookingData.destinationId);
  const [allDestinations, setAllDestinations] = useState<Destination[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const isMobile = useMediaQuery({ query: '(max-width: 768px)' });

  // Fetch destinations from API
  useEffect(() => {
    const fetchDestinations = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('https://api.tripxplo.com/v1/api/user/package/destination/search');
        const data = await response.json();
        if (data.result) {
          // Add some popular destinations with images
          const destinationsWithImages = data.result.map((dest: any) => ({
            ...dest,
            image: getDestinationImage(dest.destinationName, dest.image),
            popular: isPopularDestination(dest.destinationName),
            imageError: false
          }));
          setAllDestinations(destinationsWithImages);
        }
      } catch (error) {
        console.error('Error fetching destinations:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDestinations();
  }, []);

  // Fetch filtered destinations when search query changes
  useEffect(() => {
    const fetchFilteredDestinations = async () => {
      try {
        setIsLoading(true);
        let url = 'https://api.tripxplo.com/v1/api/user/package/destination/search';
        
        if (searchQuery.trim() !== '') {
          url += `?search=${encodeURIComponent(searchQuery)}`;
        }

        const response = await fetch(url);
        const data = await response.json();
        if (data.result) {
          // Add some popular destinations with images
          const destinationsWithImages = data.result.map((dest: any) => ({
            ...dest,
            image: getDestinationImage(dest.destinationName, dest.image),
            popular: isPopularDestination(dest.destinationName),
            imageError: false
          }));
          setAllDestinations(destinationsWithImages);
        }
      } catch (error) {
        console.error('Error fetching filtered destinations:', error);
      } finally {
        setIsLoading(false);
      }
    };

    // Debounce the search to avoid too many API calls
    const timeoutId = setTimeout(() => {
      fetchFilteredDestinations();
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  // Helper function to get destination images
  const getDestinationImage = (name: string, apiImage?: string) => {
    // First priority: API image
    if (apiImage) {
      return NEXT_PUBLIC_IMAGE_URL + apiImage;
    }
    
    // Second priority: Local image fallback
    const imageMap: { [key: string]: string } = {
      'Bali': '/bali.jpg',
      'Goa': '/goa.jpg',
      'Kashmir': '/kashmir.jpg',
      'Manali': '/manali.jpg',
      'Kodaikanal': '/kodaikanal.jpg',
      'Maldives': '/maldives.jpg',
      'Meghalaya': '/meghalaya.jpg',
      'Ooty': '/ooty.jpg',
      'Varkala': '/Varkala.jpg',
    };
    
    if (imageMap[name]) {
      return imageMap[name];
    }
    
    // Third priority: No image available, return null for text-only display
    return null;
  };

  // Helper function to determine popular destinations
  const isPopularDestination = (name: string) => {
    const popularNames = ['Bali', 'Goa', 'Manali', 'Varkala', 'Kashmir', 'Kerala'];
    return popularNames.some(popular => name.toLowerCase().includes(popular.toLowerCase()));
  };

  const handleSelectDestination = (destination: Destination) => {
    setSelectedDestination(destination.destinationName);
    setSelectedDestinationId(destination.destinationId);
    if (isMobile) {
      onContinue(destination.destinationName, destination.destinationId);
    }
  };

  const handleImageError = (destinationId: string) => {
    setAllDestinations(prevDestinations =>
      prevDestinations.map(dest =>
        dest.destinationId === destinationId ? { ...dest, imageError: true } : dest
      )
    );
  };

  const FEATURED_DESTINATIONS = [
    { name: 'Goa', tag: 'POPULAR', color: 'bg-purple-100 text-purple-800', isDomestic: true },
    { name: 'Kashmir', tag: 'HONEYMOON', color: 'bg-pink-100 text-pink-800', isDomestic: true },
    { name: 'Manali', tag:'HONEYMOON', color: 'bg-green-100 text-green-800', isDomestic: true },
    { name: 'Ooty', tag: 'BUDGET', color: 'bg-orange-100 text-orange-800', isDomestic: true },
    { name: 'Munnar', tag: 'TRENDING', color: 'bg-blue-100 text-blue-800', isDomestic: true },
    { name: 'Andaman', tag: 'IN SEASON', color: 'bg-green-100 text-green-800', isDomestic: true },
    { name: 'Kodaikanal', tag: 'IN SEASON', color: 'bg-orange-100 text-orange-800', isDomestic: true },
    { name: 'Coorg', tag: 'BUDGET', color: 'bg-blue-100 text-blue-800', isDomestic: true },
    { name: 'Alleppey', tag: 'BACKWATERS', color: 'bg-green-100 text-green-800', isDomestic: true },
    { name: 'Kochi', tag: 'BUDGET', color: 'bg-blue-100 text-blue-800', isDomestic: true },
    { name: 'Shimla', tag: 'BUDGET', color: 'bg-orange-100 text-orange-800', isDomestic: true },
    { name: 'Yelagiri', tag: '', color: '', isDomestic: true },
    { name: 'Wayanad', tag: '', color: '', isDomestic: true },
    { name: 'Meghalaya', tag: '', color: '', isDomestic: true },
    { name: 'Darjeeling', tag: '', color: '', isDomestic: true },
    { name: 'Sikkim', tag: '', color: '', isDomestic: true },
    { name: 'Delhi', tag: '', color: '', isDomestic: true },
    { name: 'Agra', tag: '', color: '', isDomestic: true },
    { name: 'Pondicherry', tag: '', color: '', isDomestic: true },
    { name: 'Madurai', tag: '', color: '', isDomestic: true },
    { name: 'Rameswaram', tag: '', color: '', isDomestic: true },
    { name: 'Ladakh', tag: '', color: '', isDomestic: true },
    { name: 'Mysore', tag: '', color: '', isDomestic: true },
    // International destinations second
    { name: 'Bali', tag: 'HONEYMOON', color: 'bg-green-100 text-green-800', isDomestic: false },
    { name: 'Maldives', tag: 'HONEYMOON', color: 'bg-pink-100 text-pink-800', isDomestic: false },
    { name: 'Europe', tag: 'IN SEASON', color: 'bg-blue-100 text-blue-800', isDomestic: false },
    { name: 'Thailand', tag: 'BUDGET', color: 'bg-orange-100 text-orange-800', isDomestic: false },
    { name: 'Singapore', tag: 'FAMILY', color: 'bg-red-100 text-red-800', isDomestic: false },
    { name: 'Abu Dhabi', tag: 'POPULAR', color: 'bg-purple-100 text-purple-800', isDomestic: false },
    { name: 'Vietnam', tag: '', color: '', isDomestic: false },
    { name: 'Dubai', tag: '', color: '', isDomestic: false },
    { name: 'Australia', tag: '', color: '', isDomestic: false },
  ];

  const sortedDestinations = [...allDestinations]
    .filter(dest => dest.image && !dest.imageError)
    .sort((a, b) => {
      const aImage = a.image && !a.imageError;
      const bImage = b.image && !b.imageError;
      if (aImage && !bImage) return -1;
      if (!aImage && bImage) return 1;
      return 0;
    });

  return (
    <PageLayout
      currentStep={0}
      title="Where do you wanna travel?"
      backgroundImage="/1976998.jpg"
      leftButton={
        <Button variant="outline" className="w-auto px-3 py-3 text-base md:px-8 md:py-4 md:text-lg bg-white border-2 border-gray-200 rounded-full shadow-md transition-all duration-300 transform md:hover:bg-gray-50 md:hover:border-gray-300 md:hover:shadow-lg md:hover:scale-105" onClick={() => router.push('/')}>
          <ArrowLeft className="w-5 h-5" />
          <span className="hidden md:inline md:ml-2">Back</span>
        </Button>
      }
      bottomRightButton={
        <Button
          className="w-full md:w-auto px-6 py-3 text-base md:px-10 md:py-6 md:text-2xl bg-[#ff7865] hover:bg-[#ff6347] text-white rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 relative overflow-hidden group"
          onClick={() => selectedDestination && selectedDestinationId && onContinue(selectedDestination, selectedDestinationId)}
          disabled={!selectedDestination}
        >
          <span className="relative flex items-center justify-center">
            Continue
            <ArrowRight className="w-5 h-5 ml-2 transition-transform group-hover:translate-x-1" />
          </span>
        </Button>
      }
      bookingData={bookingData}
      onNavigate={onNavigate}
    >
      <div className="max-w-4xl mx-auto relative">
        {/* Enhanced Search Bar */}
        <div className="relative group mb-8">
          <div className="relative bg-white/95 backdrop-blur-sm rounded-full p-1.5 md:p-2 shadow-2xl hover:shadow-3xl transition-all duration-300 border-2 border-gray-100 hover:border-gray-200">
            <div className="flex items-center">
              <div className="ml-3 md:ml-6 p-2 rounded-full bg-gradient-to-br from-blue-100 to-indigo-100 group-hover:from-blue-200 group-hover:to-indigo-200 transition-all duration-300">
                <Search className="text-blue-600 w-4 h-4 md:w-5 md:h-5" />
              </div>
              <Input
                placeholder="Search destinations..."
                className="flex-1 border-0 bg-transparent px-3 py-3 text-base md:px-4 md:py-4 md:text-lg placeholder:text-gray-500 focus:ring-0 focus:ring-offset-0 focus:border-transparent focus-visible:ring-0 focus-visible:ring-offset-0 font-medium"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onFocus={() => isMobile && setShowDestinationPicker(true)}
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSearchQuery('')}
                  className="mr-1.5 md:mr-2 rounded-full hover:bg-red-50 transition-all duration-200"
                >
                  <XCircle className="w-5 h-5 text-gray-500 hover:text-red-500 transition-colors duration-200" />
                </Button>
              )}
            </div>
          </div>
          {isMobile && showDestinationPicker && (
            <div className="absolute top-full left-0 right-0 bg-white rounded-2xl shadow-2xl mt-2 z-50 p-4 border max-h-96 overflow-y-auto">
               {/* Domestic Section */}
                {FEATURED_DESTINATIONS.filter(dest =>
                  dest.isDomestic &&
                  allDestinations.some(backendDest => backendDest.destinationName === dest.name && backendDest.image) &&
                  dest.name.toLowerCase().includes(searchQuery.toLowerCase())
                ).length > 0 && (
                    <div className="mb-4">
                      <h4 className="text-sm font-bold text-blue-600 mb-2 px-3 py-2 bg-blue-50">🇮🇳 DOMESTIC DESTINATIONS</h4>
                      <div className="space-y-1">
                        {FEATURED_DESTINATIONS.filter(dest =>
                          dest.isDomestic &&
                          allDestinations.some(backendDest => backendDest.destinationName === dest.name && backendDest.image) &&
                          dest.name.toLowerCase().includes(searchQuery.toLowerCase())
                        ).map((dest, index) => (
                          <div
                            key={`domestic-${index}`}
                            onClick={() => {
                              const selectedDest = allDestinations.find(d => d.destinationName === dest.name);
                              if (selectedDest) {
                                onContinue(selectedDest.destinationName, selectedDest.destinationId);
                              }
                              setShowDestinationPicker(false);
                              setDestinationSearch('');
                            }}
                            className="flex items-center justify-between p-3 hover:bg-gray-50 cursor-pointer transition-colors group"
                          >
                            <span className="text-gray-800 font-medium group-hover:text-gray-900">
                              {dest.name}
                            </span>
                            {dest.tag && (
                              <span className={`px-2 py-1 text-xs font-medium rounded-full ${dest.color}`}>
                                {dest.tag}
                              </span>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                {/* International Section */}
                {FEATURED_DESTINATIONS.filter(dest =>
                  !dest.isDomestic &&
                  allDestinations.some(backendDest => backendDest.destinationName === dest.name && backendDest.image) &&
                  dest.name.toLowerCase().includes(searchQuery.toLowerCase())
                ).length > 0 && (
                    <div className="border-t pt-4">
                      <h4 className="text-sm font-bold text-green-600 mb-2 px-3 py-2 bg-green-50">🌍 INTERNATIONAL DESTINATIONS</h4>
                      <div className="space-y-1">
                        {FEATURED_DESTINATIONS.filter(dest =>
                          !dest.isDomestic &&
                          allDestinations.some(backendDest => backendDest.destinationName === dest.name && backendDest.image) &&
                          dest.name.toLowerCase().includes(searchQuery.toLowerCase())
                        ).map((dest, index) => (
                          <div
                            key={`international-${index}`}
                            onClick={() => {
                              const selectedDest = allDestinations.find(d => d.destinationName === dest.name);
                              if (selectedDest) {
                                onContinue(selectedDest.destinationName, selectedDest.destinationId);
                              }
                              setShowDestinationPicker(false);
                              setDestinationSearch('');
                            }}
                            className="flex items-center justify-between p-3 hover:bg-gray-50 cursor-pointer transition-colors group"
                          >
                            <span className="text-gray-800 font-medium group-hover:text-gray-900">
                              {dest.name}
                            </span>
                            {dest.tag && (
                              <span className={`px-2 py-1 text-xs font-medium rounded-full ${dest.color}`}>
                                {dest.tag}
                              </span>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
            </div>
          )}
        </div>

        {/* Destination Cards */}
        <div className={cn("space-y-12", isMobile && showDestinationPicker && "hidden")}>
          {isLoading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {[...Array(8)].map((_, index) => (
                <div key={index} className="w-full animate-pulse h-48 bg-slate-200 rounded-2xl"></div>
              ))}
            </div>
          ) : sortedDestinations.length > 0 ? (
            <div>
              <h2 className="lg:text-2xl sm:text-lg font-bold text-gray-800 mb-6">{searchQuery ? 'Search Results' : 'Popular Destinations'}</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
                {sortedDestinations.map((dest) => (
                  <Card
                    key={dest.destinationId}
                    className={cn(
                      'cursor-pointer transition-all duration-500 rounded-2xl overflow-hidden group border-2 shadow-lg hover:shadow-2xl transform hover:-translate-y-2 hover:scale-[1.02]',
                      selectedDestination === dest.destinationName
                        ? 'border-[#23cd92] shadow-xl ring-4 ring-[#23cd92]/20 scale-[1.02] -translate-y-1'
                        : 'border-gray-200/50 hover:border-[#23cd92]/50 hover:ring-2 hover:ring-[#23cd92]/10'
                    )}
                    onClick={() => handleSelectDestination(dest)}
                  >
                    <CardContent className="p-0 relative overflow-hidden">
                      {/* Popular Badge with enhanced styling */}
                      {dest.popular && (
                        <div className="absolute top-3 left-3 bg-gradient-to-r from-[#ff7865] to-[#f95d47] text-white text-[10px] md:text-xs font-bold px-3 py-1.5 rounded-full z-20 shadow-lg flex items-center gap-1">
                          <Star className="w-3 h-3 fill-current" />
                          Trending
                        </div>
                      )}

                      {/* Hover overlay with enhanced gradient */}
                      <div className="absolute inset-0 bg-gradient-to-br from-[#ff7865]/10 via-transparent to-[#23cd92]/10 opacity-0 group-hover:opacity-100 transition-all duration-500 z-10"></div>

                      {/* Selection indicator with animation */}
                      {selectedDestination === dest.destinationName && (
                        <div className="absolute top-3 right-3 w-8 h-8 md:w-9 md:h-9 bg-gradient-to-br from-[#23cd92] to-[#1fb584] rounded-full flex items-center justify-center shadow-lg z-20 animate-pulse">
                          <CheckCircle2 className="w-5 h-5 md:w-6 md:h-6 text-white" />
                        </div>
                      )}

                      {dest.image && !dest.imageError ? (
                        <>
                          <div className="relative overflow-hidden">
                            <img
                              src={dest.image}
                              alt={dest.destinationName}
                              className="w-full h-28 md:h-40 object-cover transition-transform duration-500 group-hover:scale-110"
                              onError={() => handleImageError(dest.destinationId)}
                            />
                            {/* Image overlay gradient */}
                            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                          </div>
                          <div className="p-4 md:p-5 relative z-10 bg-white">
                            <h3 className="text-base md:text-lg font-bold text-gray-800 group-hover:text-gray-900 transition-colors duration-200 mb-1">
                              {dest.destinationName}
                            </h3>
                          </div>
                        </>
                      ) : (
                        <div className="p-5 md:p-6 flex items-center justify-center h-28 md:h-40 bg-gradient-to-br from-gray-50 via-white to-gray-100 group-hover:from-blue-50 group-hover:via-white group-hover:to-indigo-50 transition-all duration-300">
                          <div className="text-center">
                            <div className="w-12 h-12 md:w-14 md:h-14 bg-gradient-to-br from-gray-200 to-gray-300 group-hover:from-blue-200 group-hover:to-indigo-300 rounded-full flex items-center justify-center mx-auto mb-3 transition-all duration-300 group-hover:scale-110">
                              <MapPin className="w-6 h-6 md:w-7 md:h-7 text-gray-500 group-hover:text-blue-600 transition-colors duration-300" />
                            </div>
                            <h3 className="text-base md:text-lg font-bold text-gray-800 group-hover:text-gray-900 transition-colors duration-200">
                              {dest.destinationName}
                            </h3>
                            <p className="text-xs text-gray-500 mt-1 group-hover:text-gray-600 transition-colors duration-200">
                              Discover this destination
                            </p>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-16 bg-white/80 rounded-2xl shadow-lg">
              <MapPin className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-lg font-medium text-gray-900">No destinations found</h3>
              <p className="mt-1 text-sm text-gray-500">
                We couldn&apos;t find any destinations matching your search. Try something else.
              </p>
            </div>
          )}
        </div>
      </div>
    </PageLayout>
  );
};

// 2. Departure Date Page
const DepartureDate = ({ onContinue, onBack, selectedDate, bookingData, onNavigate }: { onContinue: (date: Date) => void; onBack: () => void; selectedDate: Date | undefined; bookingData: BookingData; onNavigate: (step: number) => void }) => {
  const [date, setDate] = useState<Date | undefined>(selectedDate);
  const isMobile = useMediaQuery({ query: '(max-width: 768px)' });

  const handleSelectDate = (newDate: Date | undefined) => {
    setDate(newDate);
    if (isMobile && newDate) {
      onContinue(newDate);
    }
  };

  return (
    <PageLayout
      currentStep={1}
      
      title="When do you want to travel?"
      backgroundImage="/1976998.jpg"
      leftButton={
        <Button variant="outline" className="w-auto px-3 py-3 text-base md:px-8 md:py-4 md:text-lg bg-white border-2 border-gray-200 rounded-full shadow-md transition-all duration-300 transform md:hover:bg-gray-50 md:hover:border-gray-300 md:hover:shadow-lg md:hover:scale-105" onClick={onBack}>
          <ArrowLeft className="w-5 h-5" />
          <span className="hidden md:inline md:ml-2">Back</span>
        </Button>
      }
      bottomRightButton={
        <Button
          className="w-full md:w-auto px-6 py-3 text-base md:px-10 md:py-6 md:text-2xl bg-[#ff7865] hover:bg-[#ff6347] text-white rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 relative overflow-hidden group"
          onClick={() => date && onContinue(date)}
          disabled={!date}
        >
          <span className="relative flex items-center justify-center">
            Continue
            <ArrowRight className="w-5 h-5 ml-2 transition-transform group-hover:translate-x-1" />
          </span>
        </Button>
      }
      bookingData={bookingData}
      onNavigate={onNavigate}
    >
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-center mb-8">
          <div className="bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl border-2 border-gray-100 p-6 md:p-8 hover:shadow-3xl transition-all duration-300">
            <Calendar
              mode="single"
              selected={date}
              onSelect={handleSelectDate}
              initialFocus
              disabled={(date) => date < new Date() || date < new Date("1900-01-01")}
              className="w-full"
              classNames={{
                table: "w-full border-spacing-1",
                head_cell: "w-full md:w-[50px] font-semibold text-sm md:text-base text-gray-700 pb-2 text-center",
                cell: "w-full h-10 sm:w-[51px] md:h-[50px] lg:w-[50px] md:h-[50px] p-0.5",
                row: "flex w-full justify-stretch gap-6",
                day: "rounded-xl w-full h-full text-sm md:text-base font-medium hover:bg-blue-50 hover:text-blue-600 transition-all duration-200 hover:scale-105 hover:shadow-sm flex items-center justify-center",
                day_selected: "bg-gradient-to-br from-[#ff7865] to-[#f95d47] text-white rounded-xl shadow-lg hover:shadow-xl transform scale-105",
                day_today: "bg-gradient-to-br from-[#23cd92] to-[#1fb584] text-white rounded-xl shadow-md",
                day_disabled: "opacity-40 cursor-not-allowed hover:bg-transparent hover:text-gray-400 hover:scale-100",
                caption_label: "text-lg md:text-xl font-bold text-gray-800 mb-2",
                nav_button: "rounded-full w-8 h-8 md:w-10 md:h-10 hover:bg-gray-100 transition-all duration-200 hover:scale-110 border border-gray-200 shadow-sm",
                nav_button_previous: " hover:bg-blue-50 hover:border-blue-200",
                nav_button_next: "hover:bg-blue-50 hover:border-blue-200",
                caption: "flex justify-center items-center mb-4 relative",
              }}
            />
          </div>
        </div>

        {date && (
          <div className="w-full flex justify-center mb-6 md:mb-8 px-4">
            <div className="inline-block text-base md:text-lg text-gray-700 transition-all duration-300 ease-in-out transform hover:scale-105">
              <span className="font-semibold text-white bg-[#23cd92] py-1.5 px-3 md:py-2 md:px-4 rounded-full shadow-lg">
                Selected Date: {format(date, "PPP")}
              </span>
            </div>
          </div>
        )}
      </div>
    </PageLayout>
  );
};

// 3. Rooms & Travelers Page
const Counter = ({ value, onValueChange, min = 1, max = 20, icon: Icon, label, selectedValue }: { value: number, onValueChange: (newValue: number) => void, min?: number, max?: number, icon: React.ElementType, label: string, selectedValue?: string }) => (
  <div className="bg-white/90 backdrop-blur-sm border-2 border-gray-200/60 rounded-2xl p-4 md:p-5 shadow-lg hover:shadow-xl transition-all duration-300 h-full flex flex-col justify-between hover:border-orange-200/80 group">
    <div>
      <div className="flex items-center text-gray-700 mb-3">
        <div className="w-8 h-8 bg-gradient-to-br from-orange-400 to-red-400 rounded-full flex items-center justify-center mr-3 shadow-sm group-hover:shadow-md transition-all duration-300">
          <Icon className="w-4 h-4 text-white" />
        </div>
        <h3 className="text-lg font-bold text-gray-800 group-hover:text-gray-900 transition-colors duration-200">{label}</h3>
      </div>
      {selectedValue && (
        <div className="text-left text-sm text-gray-600 mb-3 font-medium bg-gray-50 px-3 py-1 rounded-full">
          {selectedValue}
        </div>
      )}
    </div>
    <div className="flex items-center justify-between bg-gradient-to-r from-gray-100/80 to-gray-200/60 rounded-full p-1 md:p-1.5 shadow-inner">
      <Button
        variant="ghost"
        size="icon"
        className="w-10 h-10 md:w-11 md:h-11 rounded-full bg-white shadow-md text-lg font-bold text-gray-700 hover:bg-red-50 hover:text-red-600 disabled:opacity-40 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-110 active:scale-95"
        onClick={() => onValueChange(Math.max(min, value - 1))}
        disabled={value <= min}
      >
        -
      </Button>
      <span className="text-xl md:text-2xl font-bold text-gray-800 w-12 md:w-14 text-center bg-white/80 rounded-full py-1 shadow-sm">
        {value}
      </span>
      <Button
        variant="ghost"
        size="icon"
        className="w-10 h-10 md:w-11 md:h-11 rounded-full bg-white shadow-md text-lg font-bold text-gray-700 hover:bg-green-50 hover:text-green-600 disabled:opacity-40 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-110 active:scale-95"
        onClick={() => onValueChange(Math.min(max, value + 1))}
        disabled={value >= max}
      >
        +
      </Button>
    </div>
  </div>
);

const RoomsAndTravelers = ({ onContinue, onBack, initialRooms, initialAdults, initialChildren, selectedTheme, selectedThemeId, onThemeChange, bookingData, onNavigate }: { onContinue: (rooms: number, adults: number, children: number) => void; onBack: () => void; initialRooms: number; initialAdults: number; initialChildren: number; selectedTheme?: string; selectedThemeId?: string; onThemeChange?: (theme: string, themeId: string) => void; bookingData: BookingData; onNavigate: (step: number) => void }) => {
  const [adults, setAdults] = useState(initialAdults);
  const [children, setChildren] = useState(initialChildren);
  const [rooms, setRooms] = useState(initialRooms);
  const [focused, setFocused] = useState(selectedTheme || '');
  const { data: themes, isLoading: themesLoading } = useQuery<Interest[]>("fetch Interest", getInterest);

  // --- Start of logic from OptionsBox.tsx ---
  const [minRooms, setMinRooms] = useState(1);
  const [showChild, setShowChild] = useState(true);

  useEffect(() => {
    let r = 0;
    if (children == 0) {
      if (focused !== "Honeymoon") r = Math.ceil(adults / 3);
      else r = Math.ceil(adults / 2);
    } else {
      let x = adults;
      let y = children;
      r = 0;
      while (x >= 3 && y >= 1) {
        x = x - 3;
        y = y - 1;
        r++;
      }
      while (x > 0 || y > 0) {
        x = x - 3;
        y = y - 3;
        r++;
      }
    }
    setRooms(r);
    setMinRooms(r);
  }, [children, adults, focused]);

  useEffect(() => {
    switch (focused) {
      case "Couple": {
        setShowChild(true);
        break;
      }
      case "Honeymoon": {
        setAdults(2)
        setChildren(0);
        setShowChild(false);
        break;
      }
      default: {
        setShowChild(true);
      }
    }
  }, [focused]);
  // --- End of logic from OptionsBox.tsx ---


  const handleThemeFocus = (theme: Interest) => {
    setFocused(theme.interestName);
    if (onThemeChange) {
      onThemeChange(theme.interestName, theme.interestId);
    }

    if (theme.interestName === 'Honeymoon' || theme.interestName === 'Couple') {
      setAdults(2);
      setChildren(0);
    } else if (theme.interestName === 'Family') {
      setAdults(2);
      setChildren(2);
    } else if (theme.interestName === 'Friends') {
      setAdults(4);
      setChildren(0);
    }
  };

  const handleAdultsChange = (newAdults: number) => {
    if (focused === "Honeymoon" && newAdults < 2) {
        return;
    }
    setAdults(newAdults);
  }

  const handleChildrenChange = (newChildren: number) => {
    if (!showChild) {
        setChildren(0);
        return;
    }
    setChildren(newChildren);
  }



  return (
    <PageLayout
      currentStep={2}
      title="How many people are traveling?"
      backgroundImage="/1976998.jpg"
      leftButton={
        <Button variant="outline" className="w-full sm:w-auto sm:px-2 md:px-8 py-4 text-lg bg-white hover:bg-gray-50 border-2 border-gray-200 hover:border-gray-300 rounded-full shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105" onClick={onBack}>
          <ArrowLeft className="w-5 h-5 md:mr-2" />
          <span className="hidden md:inline">Back</span>
        </Button>
      }
      bookingData={bookingData}
      onNavigate={onNavigate}
    >
      <div className="max-w-6xl mx-auto px-2 md:px-0">
        {/* Main Content Grid: Left - counters, Right - summary */}
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 md:gap-12">
          {/* Left: Themes + Counters */}
          <div className="lg:col-span-3 space-y-6 md:space-y-8">
            <div className="bg-white/80 backdrop-blur-sm border border-gray-200/90 rounded-2xl p-4 md:p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-center text-gray-700 mb-4 md:mb-6">
                <Sparkles className="w-5 h-5 md:w-6 md:h-6 mr-2 md:mr-3 text-[#ff7865]" />
                <h3 className="text-lg md:text-xl font-semibold">What type of trip are you planning?</h3>
              </div>
              {themesLoading ? (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 md:gap-4">
                  {[...Array(8)].map((_, index) => (
                    <div key={index} className="w-full animate-pulse h-20 md:h-24 bg-slate-200 rounded-xl"></div>
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
                  {themes?.map((theme) => {
                    const isSelected = focused === theme.interestName;
                    return (
                      <Card
                        key={theme._id}
                        className={cn(
                          'relative cursor-pointer transition-all duration-500 rounded-xl overflow-hidden group',
                          'transform hover:-translate-y-2 hover:shadow-xl hover:scale-105',
                          isSelected
                            ? 'ring-3 ring-[#ff7865] shadow-xl bg-gradient-to-br from-orange-100 via-red-100 to-pink-100 text-gray-800 scale-105 -translate-y-1'
                            : 'bg-white/90 backdrop-blur-sm border-2 border-gray-200/60 hover:border-[#ff7865]/60 hover:bg-white',
                          'hover:ring-2 hover:ring-[#ff7865]/40'
                        )}
                        onClick={() => handleThemeFocus(theme)}
                      >
                        <CardContent className="p-3 sm:p-4 flex flex-col items-center justify-center h-24 sm:h-28 relative overflow-hidden">
                          {/* Selection indicator */}
                          {isSelected && (
                            <div className="absolute top-1 right-1 w-5 h-5 bg-[#ff7865] rounded-full flex items-center justify-center shadow-md">
                              <CheckCircle2 className="w-3 h-3 text-white" />
                            </div>
                          )}

                          {/* Theme icon with enhanced styling */}
                          <div className="relative h-8 w-8 sm:h-10 sm:w-10 mb-3 flex items-center justify-center">
                            {theme.image ? (
                              <div className={cn(
                                'w-full h-full rounded-full p-1 transition-all duration-300',
                                isSelected
                                  ? 'bg-gradient-to-br from-white/80 to-white/60 shadow-lg'
                                  : 'group-hover:bg-gradient-to-br group-hover:from-orange-50 group-hover:to-red-50'
                              )}>
                                <img
                                  src={`https://tripemilestone.in-maa-1.linodeobjects.com/${theme.image}`}
                                  alt={theme.interestName}
                                  className={cn(
                                    'w-full h-full object-contain transition-transform duration-300 rounded-full',
                                    isSelected ? 'scale-110' : 'group-hover:scale-110'
                                  )}
                                  onError={(e) => {
                                    const target = e.target as HTMLImageElement;
                                    target.onerror = null;
                                    target.src = '/placeholder-theme.svg';
                                  }}
                                />
                              </div>
                            ) : (
                              <div className={cn(
                                'w-full h-full rounded-full flex items-center justify-center transition-all duration-300',
                                isSelected
                                  ? 'bg-gradient-to-br from-orange-200 to-red-200'
                                  : 'bg-gray-100 group-hover:bg-gradient-to-br group-hover:from-orange-100 group-hover:to-red-100'
                              )}>
                                <Sparkles className={cn(
                                  'w-4 h-4 transition-colors duration-300',
                                  isSelected ? 'text-orange-600' : 'text-gray-400 group-hover:text-orange-500'
                                )} />
                              </div>
                            )}
                          </div>

                          {/* Theme name with enhanced typography */}
                          <h3 className={cn(
                            'text-xs sm:text-sm font-bold text-center transition-all duration-300',
                            isSelected
                              ? 'text-gray-800 scale-105'
                              : 'text-gray-700 group-hover:text-gray-900 group-hover:scale-105',
                            'w-full px-1'
                          )}>
                            {theme.interestName}
                          </h3>

                          {/* Enhanced hover and selection effects */}
                          <div className={cn(
                            'absolute inset-0 transition-all duration-500',
                            isSelected
                              ? 'bg-gradient-to-br from-orange-100/50 via-red-100/30 to-pink-100/50'
                              : 'bg-gradient-to-br from-transparent via-transparent to-transparent group-hover:from-orange-50/30 group-hover:via-red-50/20 group-hover:to-pink-50/30'
                          )}></div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              )}
            </div>

            <div className="flex flex-wrap md:grid md:grid-cols-3 gap-6 md:gap-8">
              <div className="w-[calc(50%-0.75rem)] md:w-auto">
                <Counter
                  label="Adults"
                  icon={UsersRoundIcon}
                  value={adults}
                  onValueChange={handleAdultsChange}
                  min={1} max={20}
                  selectedValue={`Above 12 yrs`} />
              </div>

              {showChild &&
                <div className="w-[calc(50%-0.75rem)] md:w-auto">
                  <Counter
                    label="Children"
                    icon={Users}
                    value={children}
                    onValueChange={handleChildrenChange}
                    min={0} max={10}
                    selectedValue={`5 to 11 yrs`} />
                </div>}

              <div className="w-full md:w-auto">
                <Counter
                  label="Rooms"
                  icon={Briefcase}
                  value={rooms}
                  onValueChange={(val) => setRooms(val)}
                  min={minRooms} max={adults} />
              </div>
            </div>
          </div>

          {/* Right: Summary */}
          <aside className="lg:col-span-2">
            <div className="bg-gradient-to-br from-orange-100 via-red-100 to-pink-100 backdrop-blur-sm border-2 border-orange-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 sticky top-24 p-4 md:p-6">
              <h3 className="text-xl md:text-2xl font-bold text-gray-800 mb-4 text-center flex items-center justify-center gap-2">
                <Sparkles className="w-5 h-5 text-orange-600" />
                Trip Summary
              </h3>
              <hr className="border-orange-200/60 mb-4" />

              {/* Summary Items - Single Column Layout */}
              <div className="space-y-3">
                <div className="bg-white/80 backdrop-blur-sm rounded-lg p-3 shadow-sm hover:shadow-md hover:scale-[1.02] transition-all duration-200 group">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <MapPin className="w-4 h-4 text-white" />
                      </div>
                      <span className="text-sm font-semibold text-gray-700">Destination</span>
                    </div>
                    <span className="text-sm font-bold text-gray-800">{bookingData.destination || 'Not Set'}</span>
                  </div>
                </div>

                <div className="bg-white/80 backdrop-blur-sm rounded-lg p-3 shadow-sm hover:shadow-md hover:scale-[1.02] transition-all duration-200 group">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <CalendarIcon className="w-4 h-4 text-white" />
                      </div>
                      <span className="text-sm font-semibold text-gray-700">Date</span>
                    </div>
                    <span className="text-sm font-bold text-gray-800">{bookingData.date ? format(bookingData.date, 'PPP') : 'Not Set'}</span>
                  </div>
                </div>

                <div className="bg-white/80 backdrop-blur-sm rounded-lg p-3 shadow-sm hover:shadow-md hover:scale-[1.02] transition-all duration-200 group">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <Users className="w-4 h-4 text-white" />
                      </div>
                      <span className="text-sm font-semibold text-gray-700">Travelers</span>
                    </div>
                    <span className="text-sm font-bold text-gray-800">{adults + children}</span>
                  </div>
                </div>

                <div className="bg-white/80 backdrop-blur-sm rounded-lg p-3 shadow-sm hover:shadow-md hover:scale-[1.02] transition-all duration-200 group">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-orange-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <Briefcase className="w-4 h-4 text-white" />
                      </div>
                      <span className="text-sm font-semibold text-gray-700">Rooms</span>
                    </div>
                    <span className="text-sm font-bold text-gray-800">{rooms}</span>
                  </div>
                </div>

                {focused && (
                  <div className="bg-white/80 backdrop-blur-sm rounded-lg p-3 shadow-sm hover:shadow-md hover:scale-[1.02] transition-all duration-200 group">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gradient-to-br from-pink-500 to-pink-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                          <Sparkles className="w-4 h-4 text-white" />
                        </div>
                        <span className="text-sm font-semibold text-gray-700">Trip Theme</span>
                      </div>
                      <span className="text-sm font-bold text-gray-800">{focused}</span>
                    </div>
                  </div>
                )}
              </div>

              <div className="mt-6">
                <Button
                  className="w-full py-3 text-base md:py-4 md:text-lg bg-gradient-to-r from-[#ff7865] to-[#f95d47] text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] group focus:ring-2 focus:ring-orange-300 focus:ring-offset-2"
                  onClick={() => onContinue(rooms, adults, children)}
                >
                  <span className="relative flex items-center justify-center font-semibold">
                    Search Packages
                    <ArrowRight className="w-5 h-5 ml-2 transition-transform group-hover:translate-x-1" />
                  </span>
                </Button>
              </div>
            </div>
          </aside>
        </div>
      </div>
    </PageLayout>
  );
};

const BookingPage = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const [currentStep, setCurrentStep] = useState(0); // 0: Destination, 1: Date, 2: Rooms/Travelers
  const [bookingData, setBookingData] = useState<BookingData>({
    destination: '',
    destinationId: '',
    date: undefined,
    rooms: 1,
    adults: 1,
    children: 0,
    theme: '',
    themeId: '',
    departureCity: '',
    tripType: '',
    duration: '',
  });

  const handleNext = () => {
    setCurrentStep(prev => prev + 1);
  };

  const handleBack = () => {
    setCurrentStep(prev => prev - 1);
  };

  const handleDestinationSelect = (destination: string, destinationId: string) => {
    setBookingData(prev => ({ ...prev, destination, destinationId }));
    handleNext();
  };

  const handleThemeChange = (theme: string, themeId: string) => {
    setBookingData(prev => ({ ...prev, theme, themeId }));
  };

  const handleDateSelect = (date: Date) => {
    setBookingData(prev => ({ ...prev, date }));
    handleNext();
  };

  const handleRoomsTravelersSelect = (rooms: number, adults: number, children: number) => {
    // Save rooms and travelers
    setBookingData(prev => ({ ...prev, rooms, adults, children }));

    // Immediately finalize booking (no final page)
    const finalData = { ...bookingData, rooms, adults, children };

    // Dispatch destination and date to Redux store
    dispatch(changeDestination(finalData.destination));
    dispatch(changeDestinationId(finalData.destinationId));
    if (finalData.date) {
      dispatch(changeDate(finalData.date.toISOString()));
    }

    // Dispatch theme to Redux store
    dispatch(selectTheme({ selectedTheme: finalData.theme }));
    dispatch(selectThemeId({ selectedThemeId: finalData.themeId }));

    // Dispatch room and traveler data to Redux store
    dispatch(selectAdultsChild({
      room: {
        adult: finalData.adults,
        child: finalData.children,
        room: finalData.rooms,
      },
    }));

    // Navigate to packages page
    router.push("/packages");
  };



  const renderStep = () => {
    const navigateTo = (step: number) => setCurrentStep(step);
    switch (currentStep) {
      case 0:
        return (
          <DestinationSelection
            onContinue={handleDestinationSelect}
            bookingData={bookingData}
            onNavigate={navigateTo}
            setBookingData={setBookingData}
          />
        );
      case 1:
        return (
          <DepartureDate
            onContinue={handleDateSelect}
            onBack={handleBack}
            selectedDate={bookingData.date}
            bookingData={bookingData}
            onNavigate={navigateTo}
          />
        );
      case 2:
        return (
          <RoomsAndTravelers
            onContinue={handleRoomsTravelersSelect}
            onBack={handleBack}
            initialRooms={bookingData.rooms}
            initialAdults={bookingData.adults}
            initialChildren={bookingData.children}
            selectedTheme={bookingData.theme}
            selectedThemeId={bookingData.themeId}
            onThemeChange={handleThemeChange}
            bookingData={bookingData}
            onNavigate={navigateTo}
          />
        );
      case 3:
        return null;
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen">
      {renderStep()}
    </div>
  );
};

export default BookingPage;
