"use client";

import React, { useState } from 'react';
import { Calendar } from '@/components/ui/calendar';
import { startOfToday } from 'date-fns';

interface DatePanelProps {
  onDateSelect: (date: string) => void;
}

const DatePanel: React.FC<DatePanelProps> = ({ onDateSelect }) => {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(startOfToday());

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      setSelectedDate(date);
      onDateSelect(date.toLocaleDateString());
    }
  };

  return (
    <div className="space-y-4">
      <div className="w-full">
        <Calendar
          mode="single"
          selected={selectedDate}
          onSelect={handleDateSelect}
          disabled={(date) => date < startOfToday()}
          initialFocus
          className="w-full shadow-lg rounded-xl border border-gray-100 p-4 bg-white"
          classNames={{
            table: "w-full border-spacing-1",
            head_cell: "w-full md:w-[40px] font-semibold text-xs md:text-sm text-gray-700 pb-2 text-center",
            cell: "w-full h-8 sm:w-[40px] md:h-[40px] lg:w-[40px] md:h-[40px] p-0.5",
            row: "flex w-full justify-stretch gap-1",
            day: "rounded-lg w-full h-full text-xs md:text-sm font-medium hover:bg-app-primary/10 hover:text-app-primary transition-all duration-300 hover:scale-105 hover:shadow-md flex items-center justify-center border border-transparent hover:border-app-primary/20",
            day_selected: "bg-app-secondary text-white rounded-lg shadow-xl hover:shadow-2xl transform scale-105 border-app-secondary",
            day_today: "bg-gradient-to-br from-app-primary to-app-secondary text-white rounded-lg shadow-lg hover:shadow-xl border-transparent",
            day_disabled: "opacity-40 cursor-not-allowed hover:bg-transparent hover:text-gray-400 hover:scale-100 hover:border-transparent",
            caption_label: "text-base md:text-lg font-bold text-gray-800 mb-2",
            nav_button: "rounded-full w-6 h-6 md:w-8 md:h-8 hover:bg-app-primary/10 text-app-primary transition-all duration-300 hover:scale-110 border border-app-primary/30 shadow-sm hover:shadow-md",
            nav_button_previous: "hover:bg-app-primary/10 border-app-primary/30",
            nav_button_next: "hover:bg-app-primary/10 border-app-primary/30",
            caption: "flex justify-center items-center mb-4 relative",
          }}
        />
      </div>
    </div>
  );
};

export default DatePanel;
