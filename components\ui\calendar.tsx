"use client"

import * as React from "react"
import { ChevronLeft, ChevronRight, Info } from "lucide-react"
import { DayPicker } from "react-day-picker"
import { getAllHolidays, type Holiday } from "@/lib/indian-holidays"
import { format, startOfMonth, endOfMonth, startOfWeek, endOfWeek, addDays, isSameMonth, isSameDay, addMonths, subMonths } from 'date-fns';

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

export type CalendarProps = React.ComponentProps<typeof DayPicker>

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: CalendarProps) {
  // 1. Keep track of the currently displayed month
  const [month, setMonth] = React.useState<Date>(props.defaultMonth || new Date());

  const today = new Date();

  return (
    <TooltipProvider>
      <div className="relative w-full">
        {/* --- Mobile View --- */}
        <div className="md:hidden">
          <Card className="w-full max-w-sm h-[400px] overflow-hidden shadow-xl border-gray-200 bg-gradient-to-br from-white to-gray-50/50">
            <div className="h-full overflow-y-auto snap-y snap-mandatory scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent">
              {Array.from({ length: 12 }, (_, i) => {
                const monthDate = addMonths(new Date(), i);
                const monthStart = startOfMonth(monthDate);
                const monthEnd = endOfMonth(monthStart);
                const startDate = startOfWeek(monthStart);
                const endDate = endOfWeek(monthEnd);

                return (
                  <div key={i} className="snap-start p-4 border-b border-gray-100 last:border-b-0">
                    {/* Month Header */}
                    <div className="text-center mb-4 sticky top-0 bg-gradient-to-br from-white to-gray-50/50 z-10 py-3 backdrop-blur-sm">
                      <h3 className="text-lg font-bold text-gray-900 px-4 py-2 rounded-full inline-block shadow-sm">
                        {format(monthDate, 'MMMM yyyy')}
                      </h3>
                    </div>

                    {/* Days of Week */}
                    <div className="grid grid-cols-7 mb-3 gap-1">
                      {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, idx) => (
                        <div key={idx} className="text-center text-xs font-medium text-gray-500 p-2">
                          {day}
                        </div>
                      ))}
                    </div>
                    
                    {/* Calendar Grid */}
                    <div className="space-y-1">
                      {(() => {
                        const rows = [];
                        let days = [];
                        let day = startDate;

                        while (day <= endDate) {
                          for (let j = 0; j < 7; j++) {
                            const cloneDay = day;
                            const holidays = getAllHolidays(day);
                            const isToday = isSameDay(day, today);
                            const isCurrentMonth = isSameMonth(day, monthStart);
                            const hasHolidays = holidays.length > 0;

                            days.push(
                              <div
                                key={day.toString()}
                                className={cn(
                                  "h-10 w-10 relative cursor-pointer transition-all duration-200 rounded-lg flex items-center justify-center text-sm font-medium",
                                  isCurrentMonth ? 'text-gray-900' : 'text-gray-400',
                                  isToday && 'bg-app-primary text-white shadow-lg',
                                  hasHolidays && !isToday && holidays[0] && holidays[0].type === 'public' && 'bg-gradient-to-br from-app-secondary/20 to-app-secondary/35',
                                  hasHolidays && !isToday && holidays[0] && holidays[0].type === 'religious' && 'bg-gradient-to-br from-app-secondary/20 to-app-secondary/35',
                                  !hasHolidays && !isToday && isCurrentMonth && 'hover:bg-gray-100',
                                  !isCurrentMonth && 'opacity-50'
                                )}
                                onClick={() => {
                                  const onSelect = (props as any)?.onSelect as ((date?: Date) => void) | undefined;
                                  if (onSelect && isCurrentMonth) {
                                    onSelect(new Date(day));
                                  }
                                }}
                              >
                                {format(day, 'd')}
                              </div>
                            );
                            day = addDays(day, 1);
                          }

                          rows.push(
                            <div key={day.toString()} className="grid grid-cols-7 gap-1 justify-items-center">
                              {days}
                            </div>
                          );
                          days = [];
                        }

                        return rows;
                      })()}
                    </div>
                  </div>
                );
              })}
            </div>
          </Card>
        </div>

        {/* --- Desktop View (Custom Layout) --- */}
        <div className="hidden md:flex justify-center w-full">
          <div className="w-full">
            {(() => {
              // Derived month boundaries for the grid
              const today = new Date();
              const monthStart = startOfMonth(month);
              const monthEnd = endOfMonth(monthStart);
              const gridStart = startOfWeek(monthStart);
              const gridEnd = endOfWeek(monthEnd);

              const selectedDate = (props as any)?.selected as Date | undefined;
              const onSelect = (props as any)?.onSelect as ((date?: Date) => void) | undefined;

              const isDisabled = (date: Date): boolean => {
                const disabled = (props as any)?.disabled;
                if (!disabled) return false;
                if (typeof disabled === 'function') return !!disabled(date);
                return false;
              };

              const renderHeader = () => (
                <div className="flex items-center justify-between p-4 bg-gradient-to-r from-white via-gray-50/50 to-white rounded-t-xl shadow-sm border-b border-gray-100">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setMonth(subMonths(month, 1))}
                    className="text-gray-700 hover:text-app-primary hover:bg-app-primary/10 h-9 w-9 p-0 rounded-xl transition-all duration-300 transform hover:scale-110 hover:shadow-md"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <h2 className="text-xl font-bold bg-black bg-clip-text text-transparent">
                    {format(month, 'MMMM yyyy')}
                  </h2>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setMonth(addMonths(month, 1))}
                    className="text-gray-700 hover:text-app-primary hover:bg-app-primary/10 h-9 w-9 p-0 rounded-xl transition-all duration-300 transform hover:scale-110 hover:shadow-md"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              );

              const renderDaysOfWeek = () => {
                const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
                return (
                  <div className="grid grid-cols-7 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-gray-50/50">
                    {daysOfWeek.map((day) => (
                      <div
                        key={day}
                        className="p-3 text-center text-sm font-bold text-gray-700 border-r border-gray-100 last:border-r-0"
                      >
                        {day}
                      </div>
                    ))}
                  </div>
                );
              };

              const renderCalendarDays = () => {
                const rows: React.ReactNode[] = [];
                let days: React.ReactNode[] = [];
                let day = gridStart;
                while (day <= gridEnd) {
                  for (let i = 0; i < 7; i++) {
                    const holidays = getAllHolidays(day);
                    const hasHolidays = holidays.length > 0;
                    const isToday = isSameDay(day, today);
                    const inCurrentMonth = isSameMonth(day, monthStart);
                    const disabled = isDisabled(day);
                    const selected = selectedDate ? isSameDay(day, selectedDate) : false;

                    days.push(
                      <Tooltip key={day.toISOString()}>
                        <TooltipTrigger asChild>
                          <div
                            onClick={() => {
                              if (!disabled) {
                                onSelect?.(new Date(day));
                              }
                            }}
                            className={cn(
                              'border-r border-b border-gray-100 transition-all duration-300 relative group rounded-lg overflow-hidden',
                              'min-h-[70px]',
                              inCurrentMonth ? 'bg-white hover:bg-gray-50/50' : 'bg-gray-50/30 opacity-70',
                              isToday && 'bg-gradient-to-br from-app-primary/5 to-app-primary/15 hover:from-app-primary/10 hover:to-app-primary/25 shadow-lg',
                              hasHolidays && holidays[0] && holidays[0].type === 'public' && 'bg-gradient-to-br from-app-secondary/5 to-app-secondary/15 hover:from-app-secondary/10 hover:to-app-secondary/25',
                              hasHolidays && holidays[0] && holidays[0].type === 'religious' && 'bg-gradient-to-br from-app-secondary/5 to-app-secondary/15 hover:from-app-secondary/10 hover:to-app-secondary/25',
                              !hasHolidays && !disabled && inCurrentMonth && 'hover:bg-gradient-to-br hover:from-app-primary/5 hover:to-app-secondary/5',
                              disabled ? 'cursor-not-allowed opacity-40' : 'cursor-pointer',
                              'transform hover:scale-[1.02] hover:shadow-lg hover:z-10'
                            )}
                          >
                            <div className="h-full flex flex-col relative p-3 overflow-hidden">
                              <div
                                className={cn(
                                  'font-bold transition-all duration-300 mb-2',
                                  hasHolidays ? 'text-sm self-start' : 'text-base self-center flex-1 flex items-center justify-center',
                                  !inCurrentMonth && 'text-gray-400',
                                  isToday && 'text-black text-lg',
                                  inCurrentMonth && !isToday && hasHolidays && holidays[0] && holidays[0].type === 'public' && 'text-app-secondary',
                                  inCurrentMonth && !isToday && hasHolidays && holidays[0] && holidays[0].type === 'religious' && 'text-app-secondary',
                                  inCurrentMonth && !isToday && !hasHolidays && 'text-black'
                                )}
                              >
                                {format(day, 'd')}
                              </div>

                              {isToday && !hasHolidays && (
                                <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-app-primary/10 to-app-secondary/10 animate-pulse" />
                              )}

                              {hasHolidays && (
                                <div className="flex flex-col gap-1.5 flex-1">
                                  {holidays.slice(0, 2).map((holiday, index) => (
                                    <span
                                      key={index}
                                      className={cn(
                                        holiday.type === 'public' && 'text-blue-700 font-bold',
                                        holiday.type === 'religious' && 'text-blue-700 font-bold',
                                      )}
                                      style={{
                                        wordBreak: 'break-word',
                                        fontSize: '11px',
                                        overflowWrap: 'break-word',
                                        display: '-webkit-box',
                                        WebkitLineClamp: 2,
                                        WebkitBoxOrient: 'vertical' as const,
                                        overflow: 'hidden'
                                      }}
                                    >
                                      {holiday.name}
                                    </span>
                                  ))}
                                  {holidays.length > 2 && (
                                    <span className="inline-block rounded-lg bg-gray-100 border border-gray-200 px-2 py-1 text-[9px] text-gray-600 font-medium text-center">
                                      +{holidays.length - 2} more
                                    </span>
                                  )}
                                </div>
                              )}

                              {selected && (
                                <div className="absolute inset-0 ring-3 ring-app-primary/60 bg-app-primary/5 pointer-events-none" />
                              )}

                              {/* Enhanced hover effect */}
                              <div className="absolute inset-0 bg-gradient-to-br from-app-primary/0 to-app-secondary/0 group-hover:from-app-primary/10 group-hover:to-app-secondary/10 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none" />
                              
                              {/* Subtle animation for today */}
                              {isToday && (
                                <div className="absolute inset-0 bg-gradient-to-br from-app-primary/20 to-app-primary/20 pointer-events-none animate-pulse" />
                              )}
                            </div>
                          </div>
                        </TooltipTrigger>
                        {hasHolidays && (
                          <TooltipContent side="top" className="max-w-sm p-4 bg-white shadow-xl border-gray-200 rounded-xl">
                            <div className="space-y-3">
                              <div className="flex items-center gap-2 pb-2 border-b border-gray-100">
                                <Info className="w-4 h-4 text-app-secondary" />
                                <span className="font-semibold text-gray-900">
                                  {format(day, 'MMMM d, yyyy')}
                                </span>
                              </div>
                              {holidays.map((holiday, idx) => (
                                <div key={idx} className="flex items-start gap-3">
                                  <div className={cn(
                                    "w-3 h-3 rounded-full mt-1 flex-shrink-0 shadow-sm",
                                    holiday.type === 'public' && 'bg-app-secondary',
                                    holiday.type === 'religious' && 'bg-app-secondary',
                                  )} />
                                  <div>
                                    <p className="text-sm font-semibold text-gray-900 leading-tight">{holiday.name}</p>
                                    <p className="text-xs text-gray-500 capitalize mt-1 flex items-center gap-1">
                                      <span className={cn(
                                        "w-2 h-2 rounded-full",
                                        holiday.type === 'public' && 'bg-app-secondary',
                                        holiday.type === 'religious' && 'bg-app-secondary',
                                      )} />
                                      {holiday.type} holiday
                                    </p>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </TooltipContent>
                        )}
                      </Tooltip>
                    );

                    day = addDays(day, 1);
                  }
                  rows.push(
                    <div key={day.toISOString()} className="grid grid-cols-7">
                      {days}
                    </div>
                  );
                  days = [];
                }
                return rows;
              };

              return (
                <Card className="overflow-hidden shadow-2xl border-gray-200 w-[500px] mx-auto bg-gradient-to-br from-white to-gray-50/30 backdrop-blur-sm">
                  <div className="h-full flex flex-col">
                    {renderHeader()}
                    {renderDaysOfWeek()}
                    <div className="flex-1 overflow-auto">
                      {renderCalendarDays()}
                    </div>
                  </div>
                </Card>
              );
            })()}
          </div>
        </div>
      </div>
    </TooltipProvider>
  )
}

Calendar.displayName = "Calendar"

export { Calendar }
