"use client";

import React from 'react';
import { MapPin, X } from 'lucide-react';
import { Input } from '@/components/ui/input';

interface Destination {
  destinationId: string;
  destinationName: string;
  popular?: boolean;
  isDomestic: boolean;
}

interface WherePanelProps {
  searchQuery: string;
  location: string;
  onLocationChange: (value: string) => void;
  onLocationSelect: (location: string, locationId: string) => void;
  destinations: Destination[];
  isLoading: boolean;
  isMobile: boolean;
  featuredDestinations: { name: string; tag: string; color: string }[];
  clearLocation: () => void;
}

const WherePanel: React.FC<WherePanelProps> = ({
  searchQuery,
  location,
  onLocationChange,
  onLocationSelect,
  destinations,
  isLoading,
  isMobile,
  featuredDestinations,
  clearLocation,
}) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Where to?</h3>

      {/* Results */}
      <div className="max-h-96 overflow-y-auto">
        {isLoading ? (
          <div className="space-y-2">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="animate-pulse h-12 bg-slate-200 rounded-lg"></div>
            ))}
          </div>
        ) : destinations.length > 0 ? (
          <>
            {/* Domestic Section */}
            {destinations.filter(d => d.isDomestic).length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-bold text-blue-600 mb-2 px-3 py-2 bg-blue-50 rounded-lg">
                  DOMESTIC DESTINATIONS
                </h4>
                <div className="space-y-1">
                  {destinations.filter(d => d.isDomestic).sort((a, b) => (b.popular ? 1 : 0) - (a.popular ? 1 : 0)).map((dest) => {
                    const featured = featuredDestinations.find(f =>
                      f.name.toLowerCase() === dest.destinationName.toLowerCase()
                    );
                    return (
                      <button
                        key={dest.destinationId}
                        onClick={() => onLocationSelect(dest.destinationName, dest.destinationId)}
                        className="w-full flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors text-left"
                      >
                        <div className="flex items-center gap-3">
                          <MapPin className="w-4 h-4 text-gray-400" />
                          <span className="text-base text-gray-700">{dest.destinationName}</span>
                        </div>
                        {featured && (
                          <span className={`text-xs font-semibold px-2 py-1 rounded-full ${featured.color}`}>
                            {featured.tag}
                          </span>
                        )}
                      </button>
                    );
                  })}
                </div>
              </div>
            )}

            {/* International Section */}
            {destinations.filter(d => !d.isDomestic).length > 0 && (
              <div className="border-t pt-4">
                <h4 className="text-sm font-bold text-green-600 mb-2 px-3 py-2 bg-green-50 rounded-lg">
                  INTERNATIONAL DESTINATIONS
                </h4>
                <div className="space-y-1">
                  {destinations.filter(d => !d.isDomestic).sort((a, b) => (b.popular ? 1 : 0) - (a.popular ? 1 : 0)).map((dest) => {
                    const featured = featuredDestinations.find(f =>
                      f.name.toLowerCase() === dest.destinationName.toLowerCase()
                    );
                    return (
                      <button
                        key={dest.destinationId}
                        onClick={() => onLocationSelect(dest.destinationName, dest.destinationId)}
                        className="w-full flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors text-left"
                      >
                        <div className="flex items-center gap-3">
                          <MapPin className="w-4 h-4 text-gray-400" />
                          <span className="text-base text-gray-700">{dest.destinationName}</span>
                        </div>
                        {featured && (
                          <span className={`text-xs font-semibold px-2 py-1 rounded-full ${featured.color}`}>
                            {featured.tag}
                          </span>
                        )}
                      </button>
                    );
                  })}
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-8">
            <MapPin className="mx-auto h-8 w-8 text-gray-400 mb-2" />
            <p className="text-sm text-gray-500">No destinations found</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default WherePanel;
