"use client";

import React, { useState, useEffect } from 'react';
import { Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { useQuery } from 'react-query';
import { getInterest } from '@/app/actions/get-interest';

interface Interest {
  image: string;
  interestId: string;
  interestName: string;
  isFirst: boolean;
  sort: number;
  _id: string;
}

interface WhoPanelProps {
  guests: number;
  onGuestsChange: (guests: number) => void;
}

const WhoPanel: React.FC<WhoPanelProps> = ({ guests, onGuestsChange }) => {
  const [adults, setAdults] = useState(2);
  const [children, setChildren] = useState(0);
  const [rooms, setRooms] = useState(1);
  const [selectedTheme, setSelectedTheme] = useState('Couple');
  const [selectedThemeId, setSelectedThemeId] = useState('');
  const [focused, setFocused] = useState('Couple');
  const [minRooms, setMinRooms] = useState(1);
  const [showChild, setShowChild] = useState(true);

  // Fetch themes
  const { data: themes, isLoading: themesLoading } = useQuery<Interest[]>("fetch Interest", getInterest);

  // Update guests when adults or children change
    useEffect(() => {
    onGuestsChange(adults + children);
  }, [adults, children]);

  // Room calculation logic
  useEffect(() => {
    let r = 0;
    if (children == 0) {
      if (focused !== "Honeymoon") r = Math.ceil(adults / 3);
      else r = Math.ceil(adults / 2);
    } else {
      let x = adults;
      let y = children;
      r = 0;
      while (x >= 3 && y >= 1) {
        x = x - 3;
        y = y - 1;
        r++;
      }
      while (x > 0 || y > 0) {
        x = x - 3;
        y = y - 3;
        r++;
      }
    }
    setRooms(r);
    setMinRooms(r);
  }, [children, adults, focused]);

  useEffect(() => {
    switch (focused) {
      case "Couple": {
        setShowChild(true);
        break;
      }
      case "Honeymoon": {
        setAdults(2)
        setChildren(0);
        setShowChild(false);
        break;
      }
      default: {
        setShowChild(true);
      }
    }
  }, [focused]);

  // Ensure default selected theme id when themes load (for Couple)
  useEffect(() => {
    if (!selectedThemeId && themes && selectedTheme) {
      const found = themes.find(t => t.interestName.toLowerCase() === selectedTheme.toLowerCase());
      if (found) setSelectedThemeId(found.interestId);
    }
  }, [themes, selectedTheme, selectedThemeId]);

  const handleThemeFocus = (theme: Interest) => {
    setFocused(theme.interestName);
    setSelectedTheme(theme.interestName);
    setSelectedThemeId(theme.interestId);

    if (theme.interestName === 'Honeymoon' || theme.interestName === 'Couple') {
      setAdults(2);
      setChildren(0);
    } else if (theme.interestName === 'Family') {
      setAdults(2);
      setChildren(2);
    } else if (theme.interestName === 'Friends') {
      setAdults(4);
      setChildren(0);
    }
  };

  const handleAdultsChange = (newAdults: number) => {
    if (focused === "Honeymoon" && newAdults < 2) {
        return;
    }
    setAdults(newAdults);
  };

  const handleChildrenChange = (newChildren: number) => {
    if (!showChild) {
        setChildren(0);
        return;
    }
    setChildren(newChildren);
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Who&apos;s coming?</h3>

      <div className="space-y-6">
        {/* Themes Selection */}
        <div>
          <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
            <Sparkles className="w-4 h-4 text-orange-500" />
            Trip Theme
          </h4>
          {themesLoading ? (
            <div className="grid grid-cols-3 gap-2">
              {[...Array(6)].map((_, index) => (
                <div key={index} className="w-full animate-pulse h-16 bg-slate-200 rounded-lg"></div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-3 gap-2 mb-4">
              {themes?.slice(0, 6).map((theme) => {
                const isSelected = selectedTheme === theme.interestName;
                return (
                  <Card
                    key={theme._id}
                    className={cn(
                      'cursor-pointer transition-all duration-300 rounded-lg overflow-hidden group border',
                      isSelected
                        ? 'ring-2 ring-[#ff7865] shadow-md bg-gradient-to-br from-orange-100 via-red-100 to-pink-100 border-[#ff7865]'
                        : 'border-gray-200 hover:border-[#ff7865]/50 hover:shadow-sm'
                    )}
                    onClick={() => handleThemeFocus(theme)}
                  >
                    <CardContent className="p-2 flex flex-col items-center justify-center h-16 relative">
                      <div className="w-6 h-6 mb-1 flex items-center justify-center">
                        {theme.image ? (
                          <img
                            src={`https://tripemilestone.in-maa-1.linodeobjects.com/${theme.image}`}
                            alt={theme.interestName}
                            className="w-full h-full object-contain rounded-full"
                          />
                        ) : (
                          <Sparkles className="w-4 h-4 text-gray-400" />
                        )}
                      </div>
                      <span className="text-xs font-medium text-center text-gray-700">
                        {theme.interestName}
                      </span>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </div>

        {/* Adults */}
        <div className="flex items-center justify-between">
          <div className="flex flex-col mr-auto">
            <div className="font-medium mr-auto">Adults</div>
            <div className="text-sm text-gray-500 mr-auto">Ages 13 or above</div>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              className="w-8 h-8 rounded-full p-0 bg-transparent hover:bg-red-50 hover:text-red-600 transition-colors"
              onClick={() => handleAdultsChange(Math.max(1, adults - 1))}
              disabled={adults <= 1}
            >
              -
            </Button>
            <span className="w-8 text-center font-medium">{adults}</span>
            <Button
              variant="outline"
              size="sm"
              className="w-8 h-8 rounded-full p-0 bg-transparent hover:bg-green-50 hover:text-green-600 transition-colors"
              onClick={() => handleAdultsChange(adults + 1)}
            >
              +
            </Button>
          </div>
        </div>

        {/* Children */}
        {showChild && (
          <div className="flex items-center justify-between">
            <div className="flex flex-col">
              <div className="font-medium">Children</div>
              <div className="text-sm text-gray-500 mr-auto">Ages 5-11</div>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                className="w-8 h-8 rounded-full p-0 bg-transparent hover:bg-red-50 hover:text-red-600 transition-colors"
                onClick={() => handleChildrenChange(Math.max(0, children - 1))}
                disabled={children <= 0}
              >
                -
              </Button>
              <span className="w-8 text-center font-medium">{children}</span>
              <Button
                variant="outline"
                size="sm"
                className="w-8 h-8 rounded-full p-0 bg-transparent hover:bg-green-50 hover:text-green-600 transition-colors"
                onClick={() => handleChildrenChange(children + 1)}
              >
                +
              </Button>
            </div>
          </div>
        )}

        {/* Rooms */}
        <div className="flex items-center justify-between">
          <div className="flex flex-col mr-auto">
            <div className="font-medium mr-auto">Rooms</div>
            <div className="text-sm text-gray-500 mr-auto">Based on guests</div>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              className="w-8 h-8 rounded-full p-0 bg-transparent hover:bg-red-50 hover:text-red-600 transition-colors"
              onClick={() => setRooms(Math.max(minRooms, rooms - 1))}
              disabled={rooms <= minRooms}
            >
              -
            </Button>
            <span className="w-8 text-center font-medium">{rooms}</span>
            <Button
              variant="outline"
              size="sm"
              className="w-8 h-8 rounded-full p-0 bg-transparent hover:bg-green-50 hover:text-green-600 transition-colors"
              onClick={() => setRooms(Math.min(adults, rooms + 1))}
              disabled={rooms >= adults}
            >
              +
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WhoPanel;
